<template>
  <el-dialog :title="dialogType === 1 ? '新增预警模型' : '编辑预警模型'"
             :model-value="state.visible"
             @update:model-value="(val) => state.visible = val"
             width="800"
             @close="resetDialog(ruleFormRef)">
    <el-form :model="state.form" :rules="rules" ref="ruleFormRef" label-width="140">
      <!-- 基础设置 -->
      <div class="section-title">
        <span>基础设置</span>
      </div>
      <el-form-item label="预警名称" prop="modelName">
        <el-input v-model="state.form.modelName" maxlength="40" placeholder="请输入"/>
      </el-form-item>
      <el-form-item label="模型编号" prop="modelCode">
        <el-input v-model="state.form.modelCode" placeholder="请输入" :disabled="dialogType === 2"/>
      </el-form-item>
      <el-form-item label="业务类型" prop="businessType">
        <el-select v-model="state.form.businessType" placeholder="请选择">
          <el-option
            v-for="item in (store() as any).businessTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="预警类型" prop="alertType">
        <el-select v-model="state.form.alertType" placeholder="请选择">
          <el-option
            v-for="item in (store() as any).alertTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="是否关联规则" prop="ruleIds">
        <el-select v-model="state.form.related" placeholder="请选择" style="width: 120px;">
          <el-option label="是" :value="true" />
          <el-option label="否" :value="false" />
        </el-select>
        <el-select
          v-model="state.form.ruleIds"
          placeholder="请选择"
          v-if="state.form.related"
          style="width: 260px; margin-left: 10px;"
          multiple
          filterable
        >
          <el-option
            v-for="item in ruleOptions.filter(option => option.label !== undefined && option.value !== undefined)"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="是否生成预警消息" prop="warned">
        <el-select v-model="state.form.warned" placeholder="请选择" style="width: 120px;">
          <el-option label="是" :value= true />
          <el-option label="否" :value= false />
        </el-select>
      </el-form-item>

      <!-- 预警设置 -->
      <div v-if="state.form.warned">
      <div class="section-title">
        <span >预警设置</span>
      </div>
      <el-form-item label="预警频率" prop="frequency">
        <el-select v-model="state.frequencyType" placeholder="限制" style="width: 120px;" >
          <el-option label="限制"   :value=true />
          <el-option label="不限制" :value= false />
        </el-select>
        <div v-if="state.frequencyType">

        <span style="margin-left: 30px;">每小时 </span>
        <el-input-number
          v-model="state.form.frequency"
          :min="1"
          :max="100"
          style="width: 120px; margin-left: 10px;"
        />
        <span style="margin-left: 10px;">次</span>
        </div>
      </el-form-item>
        <el-form-item label="预警级别"  prop="warnLevel">
          <el-select v-model="state.form.warnLevel" placeholder="请选择" style="width: 120px;">
            <el-option label="高" value="HIGH" />
            <el-option label="中" value="MIDDLE" />
            <el-option label="低" value="LOW" />
          </el-select>
        </el-form-item>
      <el-form-item label="预警方式" prop="alertMethods">
        <el-checkbox v-model="state.notify.dingTalkNotify" @change="setWarnType()">钉钉通知</el-checkbox>
<!--        <el-checkbox v-model="state.notify.smsNotify" @change="setWarnType()" style="margin-left: 20px;">短信通知</el-checkbox>-->
      </el-form-item>
      <el-form-item label="钉钉群Webhook地址" prop="webhook" v-if="state.notify.dingTalkNotify">
        <el-input
          v-model="state.form.webhook"
          type="textarea"
          :rows="3"
          placeholder="请输入钉钉群Webhook地址"
        />
      </el-form-item>
      <el-form-item label="预警通知人员" prop="notifiers">
        <div class="notifiers-display">
          <el-tag
            v-for="(contact, index) in state.notify.notifiers"
            :key="contact.id"
            closable
            @close="removeNotifier(index)"
            class="notifier-tag"
          >
            {{ contact.name }}
          </el-tag>
          <el-button type="primary" size="small" v-if="state.notify.notifiers" @click="showContactSelector">{{state.notify.notifiers.length > 0 ? '编辑' :'请选择人员'}}</el-button>
        </div>
      </el-form-item>
      <el-form-item label="预警内容" prop="warnContent">
        <div class="alert-content-container">
          <div class="basic-variables">
            <div class="variables-title">基础变量</div>
            <div class="variables-list">
              <el-tag
                v-for="variable in basicVariables"
                :key="variable.key"
                @click="insertVariable(variable.key)"
                class="variable-tag"
              >
                {{ variable.label }}
              </el-tag>
            </div>
          </div>
          <div class="message-content">
            <div class="content-title">消息内容</div>
            <div class="selected-variables">
              <el-tag
                v-for="(tag, index) in state.selectedVariables"
                :key="index"
                closable
                @close="removeVariable(index)"
                class="selected-tag"
              >
                {{ tag.label }}
              </el-tag>
            </div>
            <el-input
              v-model="state.form.warnContent"
              type="textarea"
              :rows="7"
              placeholder="请输入预警消息内容"
              style="margin-top: 10px;"
              ref="warnContentRef"
              @input="handleContentChange"
            />
          </div>
        </div>
      </el-form-item>
      </div>
      <el-form-item label="模型说明" prop="description">
        <el-input
          v-model="state.form.description"
          type="textarea"
          maxlength="200"
          placeholder="请输入"
          :rows="3"
        />
      </el-form-item>
    </el-form>
    <div class="tc mt20">
      <el-button type="primary" @click="saveDialog(ruleFormRef)" :loading="state.btnLoading">保存</el-button>
      <el-button @click="$emit('update:dialogVisible', false)">取消</el-button>
    </div>

    <!-- 人员选择器组件 -->
    <ContactSelector
      v-model="state.contactSelectorVisible"
      :selected-contacts="state.notify.notifiers"
      @confirm="handleContactConfirm"
    />
  </el-dialog>
</template>

<script setup lang="ts">
import { reactive, ref, watch } from 'vue'
import {ElMessage} from 'element-plus'
import type { FormRules, FormInstance } from 'element-plus'
import WarnApi from '@/api/EarlyWarnManage'
import { store } from '@/stores'
import ContactSelector from '@/components/ContactSelector.vue'
import OrganizationApi from '@/api/Organization'

// 联系人接口定义
interface Contact {
  id: string
  name: string
  avatar?: string
  type: 'USER' | 'DEPT'
  checked?: boolean
  children?: Contact[]
}

// 参数管理
const props = defineProps<{
  dialogVisible: Boolean;
  dialogType: Number;
  dialogForm: Object;
  ruleOptions: Array<{ label: string; value: string }>;
}>();

// 基础变量列表
const basicVariables = [
  { key: 'platformName', label: '第三方名称+' },
  { key: 'period', label: '期次+' },
  { key: 'modelName', label: '预警名称+' },
  { key: 'businessType', label: '业务类型+' },
  { key: 'alertType', label: '预警类型+' },
  { key: 'triggerCondition', label: '触发条件+' },
  { key: 'reason', label: '原因+' },
  { key: 'reportTime', label: '上报时间+' }
]


// 状态管理
const state = reactive({
  visible: props.dialogVisible,
  contactSelectorVisible: false,
  frequencyType: true, // 预警频率类型
  notify: {
    dingTalkNotify: false, // 钉钉通知
    smsNotify: false, // 短信通知
    notifiers: [] as Contact[], // 预警通知人员
  },
  form: {
    id: '',
    modelName: '', // 预警名称
    modelCode: '', // 模型编号
    businessType: '', // 业务类型
    alertType: '', // 预警类型
    warnLevel: 'LOW', //预警级别
    related: true, // 是否关联规则
    ruleIds: [] as Array<string>, // 关联规则ID
    warned: true, // 是否生成预警消息
    frequency: 0, // 预警频率次数
    warnType: 0, // 预警方式
    notifiers:[] as Array<string>, // 预警通知人员
    webhook: '', // 钉钉群Webhook地址
    warnContent: '', // 预警消息内容
    description: '' // 模型说明
  },
  selectedVariables: [] as Array<{key: string, label: string}>, // 已选择的变量
  btnLoading: false
})

watch(
  () => props.dialogVisible,
  async (newVal) => {
    state.visible = newVal
    if (newVal) {
      state.form = {...state.form, ...props.dialogForm}
      // 当frequency为0时，将预警频率设置为不限制
      if (state.form.frequency === 0) {
        state.frequencyType = false
      } else {
        state.frequencyType = true
      }
      getWarnType(state.form.warnType)
      initSelectedVariables(state.form.warnContent)
      // 获取规则列表
      // 如果是编辑模式且存在通知人员ID，则获取用户详细信息
      if (state.form.notifiers && state.form.notifiers.length > 0) {
        await fetchNotifierDetails()
      }
    }
  }
)

// 监听frequencyType变化，当从不限制改为限制且frequency为0时，将frequency设置为1
watch(
  () => state.frequencyType,
  (newVal) => {
    if (newVal && state.form.frequency === 0) {
      state.form.frequency = 1
    }
  }
)

const ruleFormRef = ref<FormInstance>()
const warnContentRef = ref<any>(null) // Element Plus el-input 组件的引用

const rules = reactive<FormRules>({
  modelName: [
    { required: true, message: '请输入预警名称', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9\u4e00-\u9fa5]+$/, message: '请输入中文、数字或字母', trigger: 'blur'}
  ],
  modelCode: [
    { required: true, message: '请输入模型编号', trigger: 'blur' },
    { pattern: /^\d+$/, message: '请输入数字', trigger: 'blur' },
    { max: 6, message: '限制6位数', trigger: 'blur' },
  ],
  businessType: [{ required: true, message: '请选择业务类型', trigger: 'change' }],
  alertType: [{ required: true, message: '请选择预警类型', trigger: 'change' }],
  ruleIds: [
    {
      required: true,
      message: '请选择关联规则',
      trigger: 'change',
      validator: (rule: any, value: any, callback: any) => {
        if (state.form.related && !state.form.ruleIds) {
          callback(new Error('请选择关联规则'))
        } else {
          callback()
        }
      }
    }
  ],
  warned: [{ required: true, message: '请选择是否生成预警消息', trigger: 'change' }],
  warnLevel: [{ required: true, message: '请选择预警级别', trigger: 'change' }],
  frequency: [
    {
      required: true,
      message: '请选择预警频率',
      trigger: 'change',
      validator: (rule: any, value: any, callback: any) => {
        if (state.frequencyType && state.form.frequency === 0) {
          callback(new Error('请选择预警频率'));
        } else {
          callback();
        }
      }
    }
  ],
  alertMethods: [
    {
      required: true,
      message: '请至少选择一种预警方式',
      trigger: 'change',
      validator: (rule: any, value: any, callback: any) => {
        if (state.form.warned && !state.notify.dingTalkNotify) {
          callback(new Error('请至少选择一种预警方式'));
        } else {
          callback();
        }
      }
    }
  ],
  notifiers: [
    {
      required: true,
      message: '请选择预警通知人员',
      trigger: 'change',
      validator: (rule: any, value: any, callback: any) => {
        if (state.form.warned && (!state.notify.notifiers || state.notify.notifiers.length === 0)) {
          callback(new Error('请选择预警通知人员'));
        } else {
          callback();
        }
      }
    }
  ],
  webhook: [
    {
      required: true,
      message: '请输入钉钉群Webhook地址',
      trigger: 'blur',
      validator: (rule: any, value: string, callback: any) => {
        if (state.notify.dingTalkNotify && !value) {
          callback(new Error('请输入钉钉群Webhook地址'));
        } else {
          callback();
        }
      }
    }
  ],
  warnContent: [
    {
      required: true,
      message: '请输入预警消息内容',
      trigger: 'blur',
      validator: (rule: any, value: string, callback: any) => {
        if (state.form.warned && !value) {
          callback(new Error('请输入预警消息内容'));
        } else {
          callback();
        }
      }
    }
  ]
})

// 定义要发送的emit事件
let $emit = defineEmits(['update:dialogVisible', 'callback'])

// 根据用户ID获取用户详细信息
const fetchNotifierDetails = async () => {
  try {
    const response = await OrganizationApi.getUsersByIds(state.form.notifiers)
    if (response.code === '0000') {
      // 将获取到的用户信息转换为Contact格式
      state.notify.notifiers = response.data.map((user: any) => ({
        id: user.id,
        name: user.name,
        type: 'USER',
        checked: true
      }))
    } else {
      ElMessage.error(response.message || '获取通知人员信息失败')
    }
  } catch (error) {
    ElMessage.error('获取通知人员信息失败')
  }
}

// 插入变量到消息内容
const insertVariable = (variableKey: string) => {
  const variable = basicVariables.find(v => v.key === variableKey)
  if (variable) {
    // 检查是否已经添加过这个变量
    const isAlreadyAdded = state.selectedVariables.some(v => v.key === variableKey)
    if (isAlreadyAdded) {
      ElMessage.error('变量已添加')
      return
    }
    state.selectedVariables.push({ key: variableKey, label: variable.label })

    // 获取文本输入框元素
    const inputRef = warnContentRef.value
    if (inputRef && inputRef.$el) {
      // 对于 Element Plus 的 el-input textarea，需要获取内部的 textarea 元素
      const textarea = inputRef.$el.querySelector('textarea')
      if (textarea) {
        // 获取当前光标位置
        const startPos = textarea.selectionStart || 0
        const endPos = textarea.selectionEnd || 0
        const content = state.form.warnContent || ''

        // 在光标位置插入变量文本
        const newContent = content.substring(0, startPos) + `{${variable.label}}` + content.substring(endPos)

        // 更新内容
        state.form.warnContent = newContent

        // 设置光标位置到插入文本后
        const newCursorPos = startPos + `{${variable.label}}`.length
        setTimeout(() => {
          textarea.setSelectionRange(newCursorPos, newCursorPos)
          textarea.focus()
        }, 0)
        return
      }
    }

    // 如果无法获取到textarea元素，直接在末尾添加
    const content = state.form.warnContent || ''
    state.form.warnContent = content + `{${variable.label}}`
  }
}

// 移除消息内容中的变量
const removeVariable = (index: number) => {
  const variable = state.selectedVariables[index];
  if (variable) {
    state.selectedVariables.splice(index, 1);
    state.form.warnContent = state.form.warnContent.replaceAll(`{${variable.label}}`,'')
  }
};

const initSelectedVariables = (content: string) => {
  if (!content) {
    return
  }
  basicVariables.forEach( value => {
    // 检查不带空格的标签格式（可能是从后端获取的数据）
    if (content.includes(`{${value.key}}`)) {
      state.selectedVariables.push({ key: value.key, label: value.label })
      state.form.warnContent = state.form.warnContent.replaceAll(`{${value.key}}`,`{${value.label}}`)
    }
    // 检查带空格的标签格式（用户插入的标签）
    else if (content.includes(`  {${value.key}}  `)) {
      state.selectedVariables.push({ key: value.key, label: value.label })
      state.form.warnContent = state.form.warnContent.replaceAll(`{${value.key}}`,`{${value.label}}`)
    }
  })
}

const replaceSelectedVariables = () => {
  if (state.form.warnContent) {
    basicVariables.forEach(value => {
      if (state.form.warnContent.includes(`{${value.label}}`)) {
        state.form.warnContent = state.form.warnContent.replaceAll(`{${value.label}}`, `{${value.key}}`)
      }
    })
  }
}

//如果需要防止用户部分删除标签文本，可以添加以下逻辑：

// 处理文本框内容变化（增强版）
const handleContentChange = (value: string) => {
  let newValue = value
  const remainingVariables = []

  // 检查每个已选择的变量
  for (const variable of state.selectedVariables) {
    const variableText = `{${variable.label}}`

    // 检查变量标签文本是否还在文本框内容中
    if (value.includes(variableText)) {
      // 如果还在，保留这个变量
      remainingVariables.push(variable)
    }
  }

  // 检查是否有新的标签被添加（通过直接输入或粘贴）
  for (const variable of basicVariables) {
    const variableText = `{${variable.label}}`
    if (newValue.includes(variableText) && !remainingVariables.find(v => v.label === variable.label)) {
      remainingVariables.push(variable)
    }
  }

  // 更新selectedVariables数组
  state.selectedVariables = remainingVariables

  // 更新文本框内容
  state.form.warnContent = newValue
}

// 汇总预警方式的变量
const getWarnType = (index: number) => {
  if (index === 3) {
    state.notify.smsNotify = true
    state.notify.dingTalkNotify = true
  }else if (index === 2){
    state.notify.smsNotify = true
  }else if (index === 1){
    state.notify.dingTalkNotify = true
  }else {
    state.notify.smsNotify = false
    state.notify.dingTalkNotify = false
  }
};

const setWarnType = () => {
  if (state.notify.smsNotify && state.notify.dingTalkNotify) {
    state.form.warnType = 3
  }else if (state.notify.smsNotify){
    state.form.warnType = 2
  }else if (state.notify.dingTalkNotify) {
    state.form.warnType = 1
  }else {
    state.form.warnType = 0
  }

};

// 显示人员选择器
const showContactSelector = () => {
  state.contactSelectorVisible = true;
};

// 移除通知人员
const removeNotifier = (index: number) => {
  state.notify.notifiers.splice(index, 1);
};

// 处理人员选择确认
const handleContactConfirm = (contacts: Contact[]) => {
  state.notify.notifiers = contacts;
};

// 弹窗保存
const saveDialog = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  state.form.notifiers = state.notify.notifiers.map(item => item.id.toString())
  await formEl.validate((valid) => {
    if (valid) {
      state.btnLoading = true
      let apiName = 'addAlertModel'
      if (state.form.id) {
        apiName = 'editAlertModel'
      }
      if (!state.frequencyType) {
        state.form.frequency = 0
      }
      // 将消息内容中的label替换成key 供后端使用
      replaceSelectedVariables();
      (WarnApi as any)[apiName](state.form).then((res: any) => {
        state.btnLoading = false
        if (res.code === '0000') {
          state.visible = false
          $emit('callback')
          ElMessage({ type: 'success', message: '操作成功' })
        } else {
          ElMessage({ type: 'error', message: res.message })
        }
      })
    }
  })
}

// 重置弹窗
const resetDialog = (formEl: FormInstance | undefined) => {
  state.form = {
    id: '',
    modelName: '', // 预警名称
    modelCode: '', // 模型编号
    businessType: '', // 业务类型
    alertType: '', // 预警类型
    warnLevel: 'LOW', // 预警级别
    related: true, // 是否关联规则
    ruleIds: [] as Array<string>, // 关联规则ID
    warned: true, // 是否生成预警消息
    frequency: 0, // 预警频率次数
    warnType: 0, // 预警方式
    webhook: '', // 钉钉群Webhook地址
    notifiers: [] as Array<string>, // 预警通知人员
    warnContent: '', // 预警消息内容
    description: '' // 模型说明
  }
  state.notify = {
    dingTalkNotify: false, // 钉钉通知
    smsNotify: false,
    notifiers: [] as Contact[]// 短信通知
  }

  state.frequencyType= true // 预警频率类型
  state.selectedVariables = [] // 重置已选择的变量
  if (!formEl) return
  formEl.resetFields()
  $emit('update:dialogVisible', false)
}
</script>

<style scoped lang="scss">
.el-input {
  width: 260px;
}
.w260 {
  width: 260px;
}
.el-select {
  width: 260px;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  margin: 20px 0 15px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
}

.alert-content-container {
  display: flex;
  gap: 20px;
  width: 100%;
}

.basic-variables {
  width: 200px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 10px;
}

.variables-title {
  font-size: 14px;
  font-weight: bold;
  justify-content: center;
  margin-bottom: 10px;
  color: #606266;
}

.variables-list {
  max-height: 220px;
  overflow-y: auto;
}

.variable-tag {
  display: flex;
  justify-content: center;
  margin-bottom: 8px;
  cursor: pointer;
  background-color: #f5f7fa;
  border: 1px solid #e4e7ed;
  color: #606266;
  text-align: center;
  min-height: 8px;

  &:hover {
    background-color: #ecf5ff;
    border-color: #409eff;
    color: #409eff;
  }
}

.message-content {
  flex: 1;
}

.content-title {
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 10px;
  color: #606266;
}

.selected-variables {
  margin-bottom: 10px;
  min-height: 32px;
  padding: 5px;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  background-color: #fafafa;
}

.selected-tag {
  margin: 2px;
  background-color: #409eff;
  border-color: #409eff;
  color: #fff;

  &:hover {
    background-color: #66b1ff;
    border-color: #66b1ff;
  }
}

.tc {
  text-align: center;
}

.mt20 {
  margin-top: 20px;
}

.notifiers-display {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
  min-height: 32px;
  padding: 4px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #fafafa;
}

.notifier-tag {
  background-color: #409eff;
  border-color: #409eff;
  color: #fff;

  &:hover {
    background-color: #66b1ff;
    border-color: #66b1ff;
  }
}
</style>

